# MATLAB代码使用说明

## 文件说明

### 主要文件
1. **question_1.m** - 主分析脚本
2. **setup_chinese_font.m** - 中文字体配置脚本
3. **999.xlsx** - 数据文件

### 生成文件
- **matlab_regression_analysis.png** - 综合分析图表
- **matlab_regression_equation.png** - 回归方程展示图

## 运行步骤

### 1. 环境准备
```matlab
% 确保MATLAB版本支持以下功能：
% - readtable函数（R2013b及以上）
% - 统计工具箱
% - 图形处理功能
```

### 2. 中文乱码解决方案

#### 方法一：自动配置（推荐）
```matlab
% 运行主脚本，会自动调用字体配置
run('question_1.m')
```

#### 方法二：手动配置
```matlab
% 在运行主脚本前，先运行字体配置
setup_chinese_font();
run('question_1.m')
```

#### 方法三：系统级解决方案

**Windows系统：**
1. 确保系统已安装中文字体（SimHei、微软雅黑等）
2. 在MATLAB命令窗口执行：
```matlab
set(0,'DefaultAxesFontName','SimHei');
set(0,'DefaultTextFontName','SimHei');
```

**macOS系统：**
1. 使用系统自带的中文字体
```matlab
set(0,'DefaultAxesFontName','PingFang SC');
set(0,'DefaultTextFontName','PingFang SC');
```

**Linux系统：**
1. 安装中文字体包
```bash
sudo apt-get install fonts-wqy-microhei
```
2. 在MATLAB中设置：
```matlab
set(0,'DefaultAxesFontName','WenQuanYi Micro Hei');
set(0,'DefaultTextFontName','WenQuanYi Micro Hei');
```

### 3. 运行主脚本
```matlab
% 在MATLAB命令窗口中运行
question_1
```

## 功能说明

### 数据处理功能
- 自动读取Excel文件
- 智能识别Y浓度变量
- 处理缺失值（均值填充）
- 过滤缺失值过多的变量（>50%）

### 分析功能
- Y浓度对数变换
- 多元线性回归建模
- 模型评估（R²、MSE、RMSE）
- 统计显著性检验
- 置信区间计算

### 可视化功能
- 实际值vs预测值散点图
- 残差分析图
- 残差分布直方图
- Q-Q正态性检验图
- 回归系数柱状图
- 变量相关性热力图
- 回归方程展示图

## 输出结果

### 控制台输出
- 数据加载信息
- 预处理过程
- 回归方程
- 模型评估指标
- 统计检验结果
- 置信区间
- 分析总结

### 图形输出
- **matlab_regression_analysis.png**: 6个子图的综合分析
- **matlab_regression_equation.png**: 回归方程和评估指标展示

## 常见问题解决

### 1. 中文乱码问题
**现象**: 图表中中文显示为方框或乱码
**解决方案**:
```matlab
% 方案1: 重新设置字体
setup_chinese_font();

% 方案2: 手动设置字体
set(0,'DefaultAxesFontName','SimHei');  % Windows
set(0,'DefaultAxesFontName','PingFang SC');  % macOS

% 方案3: 使用英文标签（修改代码中的中文字符串）
```

### 2. 数据读取失败
**现象**: 无法读取Excel文件
**解决方案**:
```matlab
% 检查文件路径和名称
dir('*.xlsx')

% 手动指定文件路径
data = readtable('完整路径/999.xlsx');
```

### 3. 内存不足
**现象**: 处理大数据时内存溢出
**解决方案**:
```matlab
% 清理内存
clear; clc;

% 分批处理数据或减少变量数量
```

### 4. 图形显示问题
**现象**: 图形窗口无法正常显示
**解决方案**:
```matlab
% 重置图形设置
set(0,'DefaultFigureVisible','on');

% 手动调整图形大小
figure('Position', [100, 100, 1200, 800]);
```

## 代码自定义

### 修改分析变量
```matlab
% 在代码中找到以下部分并修改：
y_var_candidates = {'Y染色体浓度', 'Y浓度百分比', 'Y染色体的Z值'};
```

### 调整图形样式
```matlab
% 修改颜色、字体大小等
set(gca, 'FontSize', 14);
colormap('jet');  % 改变颜色映射
```

### 添加新的分析功能
```matlab
% 在主脚本末尾添加自定义分析代码
% 例如：添加预测功能、模型比较等
```

## 技术支持

如果遇到问题，请检查：
1. MATLAB版本兼容性
2. 必要工具箱是否安装
3. 数据文件格式是否正确
4. 系统字体配置是否完整

---
*最后更新: 2025年1月*
