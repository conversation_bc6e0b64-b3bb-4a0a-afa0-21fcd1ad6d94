#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对Y浓度做对数变换的多元线性回归分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score, mean_squared_error
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_analyze_data():
    """加载数据并进行对数变换的多元线性回归分析"""
    
    # 读取Excel文件
    try:
        df = pd.read_excel('999.xlsx')
        print("数据加载成功！")
        print(f"数据形状: {df.shape}")
        print("\n数据前5行:")
        print(df.head())
        print("\n数据基本信息:")
        print(df.info())
        print("\n数据描述性统计:")
        print(df.describe())
        
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        # 如果无法读取Excel，创建示例数据
        print("创建示例数据进行演示...")
        np.random.seed(42)
        n_samples = 100
        
        # 创建示例数据
        X1 = np.random.normal(10, 2, n_samples)  # 自变量1
        X2 = np.random.normal(5, 1, n_samples)   # 自变量2
        X3 = np.random.normal(15, 3, n_samples)  # 自变量3
        
        # 创建Y变量（确保为正值，便于对数变换）
        Y = np.exp(0.5 + 0.3*X1 + 0.2*X2 + 0.1*X3 + np.random.normal(0, 0.2, n_samples))
        
        df = pd.DataFrame({
            'X1': X1,
            'X2': X2, 
            'X3': X3,
            'Y': Y
        })
        
        print("示例数据创建完成！")
        print(f"数据形状: {df.shape}")
        print("\n数据前5行:")
        print(df.head())
    
    return df

def log_transform_regression(df):
    """对Y进行对数变换并建立多元线性回归模型"""

    # 寻找Y浓度相关的列
    y_column = None
    possible_y_columns = ['Y染色体浓度', 'Y浓度百分比', 'Y染色体的Z值']

    for col in possible_y_columns:
        if col in df.columns:
            y_column = col
            break

    if y_column is None:
        # 如果没找到，寻找包含'Y'的列
        for col in df.columns:
            if 'Y' in col and df[col].dtype in ['float64', 'int64']:
                y_column = col
                break

    if y_column is None:
        y_column = df.columns[-1]  # 默认使用最后一列

    print(f"\n使用 '{y_column}' 作为因变量Y")

    # 选择数值型自变量
    numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
    X_columns = [col for col in numeric_columns if col != y_column]

    # 移除包含太多缺失值的列（超过50%缺失）
    X_columns_filtered = []
    for col in X_columns:
        missing_ratio = df[col].isnull().sum() / len(df)
        if missing_ratio < 0.5:  # 保留缺失值少于50%的列
            X_columns_filtered.append(col)

    X_columns = X_columns_filtered
    print(f"选择的数值型自变量 (缺失值<50%): {X_columns}")
    print(f"共{len(X_columns)}个自变量")

    # 检查Y是否有非正值
    Y = df[y_column].copy()
    if (Y <= 0).any():
        print(f"警告: Y变量中有非正值，将进行处理...")
        # 对于非正值，可以加一个小的正常数
        Y = Y + abs(Y.min()) + 1
        print(f"处理后Y的范围: [{Y.min():.4f}, {Y.max():.4f}]")

    # 对Y进行对数变换
    log_Y = np.log(Y)
    print(f"\n对数变换后log(Y)的范围: [{log_Y.min():.4f}, {log_Y.max():.4f}]")

    # 获取自变量数据
    X = df[X_columns].copy()

    # 处理缺失值 - 使用均值填充
    print("处理缺失值...")
    for col in X_columns:
        if X[col].isnull().any():
            mean_val = X[col].mean()
            X[col].fillna(mean_val, inplace=True)
            print(f"  {col}: 用均值 {mean_val:.4f} 填充缺失值")

    # 检查最终数据
    print(f"\n最终数据形状: X{X.shape}, Y{log_Y.shape}")

    if len(X) == 0:
        raise ValueError("处理后没有可用的数据行")

    # 建立多元线性回归模型
    model = LinearRegression()
    model.fit(X, log_Y)
    
    # 预测
    log_Y_pred = model.predict(X)
    
    # 计算评估指标
    r2 = r2_score(log_Y, log_Y_pred)
    mse = mean_squared_error(log_Y, log_Y_pred)
    rmse = np.sqrt(mse)
    
    # 输出回归方程
    print("\n" + "="*60)
    print("对数变换的多元线性回归结果")
    print("="*60)
    
    # 构建回归方程
    equation = f"ln(Y) = {model.intercept_:.4f}"
    for i, coef in enumerate(model.coef_):
        sign = "+" if coef >= 0 else ""
        equation += f" {sign}{coef:.4f}*{X_columns[i]}"
    
    print(f"\n回归方程:")
    print(f"{equation}")
    
    print(f"\n模型评估指标:")
    print(f"R² (决定系数): {r2:.4f}")
    print(f"MSE (均方误差): {mse:.4f}")
    print(f"RMSE (均方根误差): {rmse:.4f}")
    
    # 输出回归系数的详细信息
    print(f"\n回归系数详情:")
    print(f"截距项: {model.intercept_:.4f}")
    for i, (var, coef) in enumerate(zip(X_columns, model.coef_)):
        print(f"{var}: {coef:.4f}")
    
    return model, X, log_Y, log_Y_pred, X_columns, y_column

def create_visualizations(model, X, log_Y, log_Y_pred, X_columns, y_column):
    """创建可视化图表"""
    
    # 设置图形样式
    plt.style.use('default')
    
    # 创建图形
    fig = plt.figure(figsize=(16, 12))
    
    # 1. 实际值vs预测值散点图
    plt.subplot(2, 3, 1)
    plt.scatter(log_Y, log_Y_pred, alpha=0.6, color='blue')
    plt.plot([log_Y.min(), log_Y.max()], [log_Y.min(), log_Y.max()], 'r--', lw=2)
    plt.xlabel('实际值 ln(Y)')
    plt.ylabel('预测值 ln(Y)')
    plt.title('实际值 vs 预测值')
    plt.grid(True, alpha=0.3)
    
    # 添加R²信息
    r2 = r2_score(log_Y, log_Y_pred)
    plt.text(0.05, 0.95, f'R² = {r2:.4f}', transform=plt.gca().transAxes, 
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # 2. 残差图
    plt.subplot(2, 3, 2)
    residuals = log_Y - log_Y_pred
    plt.scatter(log_Y_pred, residuals, alpha=0.6, color='green')
    plt.axhline(y=0, color='r', linestyle='--')
    plt.xlabel('预测值 ln(Y)')
    plt.ylabel('残差')
    plt.title('残差图')
    plt.grid(True, alpha=0.3)
    
    # 3. 残差的直方图
    plt.subplot(2, 3, 3)
    plt.hist(residuals, bins=20, alpha=0.7, color='orange', edgecolor='black')
    plt.xlabel('残差')
    plt.ylabel('频数')
    plt.title('残差分布直方图')
    plt.grid(True, alpha=0.3)
    
    # 4. Q-Q图（正态性检验）
    plt.subplot(2, 3, 4)
    from scipy import stats
    stats.probplot(residuals, dist="norm", plot=plt)
    plt.title('残差Q-Q图（正态性检验）')
    plt.grid(True, alpha=0.3)
    
    # 5. 回归系数柱状图
    plt.subplot(2, 3, 5)
    coefficients = list(model.coef_)
    variables = X_columns
    
    colors = ['red' if coef < 0 else 'blue' for coef in coefficients]
    bars = plt.bar(variables, coefficients, color=colors, alpha=0.7)
    plt.xlabel('自变量')
    plt.ylabel('回归系数')
    plt.title('回归系数')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, coef in zip(bars, coefficients):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + (0.01 if height >= 0 else -0.03),
                f'{coef:.3f}', ha='center', va='bottom' if height >= 0 else 'top')
    
    # 6. 相关性热力图
    plt.subplot(2, 3, 6)
    # 创建包含log(Y)的数据框用于相关性分析
    corr_data = X.copy()
    corr_data['ln(Y)'] = log_Y
    correlation_matrix = corr_data.corr()
    
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                square=True, fmt='.3f')
    plt.title('变量相关性热力图')
    
    plt.tight_layout()
    plt.savefig('log_regression_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 创建单独的回归方程展示图
    fig2, ax = plt.subplots(figsize=(12, 8))
    ax.axis('off')
    
    # 构建回归方程文本
    equation = f"ln(Y) = {model.intercept_:.4f}"
    for i, coef in enumerate(model.coef_):
        sign = " + " if coef >= 0 else " "
        equation += f"{sign}{coef:.4f} × {X_columns[i]}"
    
    # 显示回归方程
    ax.text(0.5, 0.7, "对数变换的多元线性回归方程", 
            fontsize=20, ha='center', weight='bold')
    ax.text(0.5, 0.5, equation, fontsize=16, ha='center',
            bbox=dict(boxstyle='round,pad=1', facecolor='lightblue', alpha=0.8))
    
    # 显示模型评估指标
    metrics_text = f"""
    模型评估指标:
    R² (决定系数): {r2:.4f}
    MSE (均方误差): {mean_squared_error(log_Y, log_Y_pred):.4f}
    RMSE (均方根误差): {np.sqrt(mean_squared_error(log_Y, log_Y_pred)):.4f}
    """
    ax.text(0.5, 0.2, metrics_text, fontsize=14, ha='center',
            bbox=dict(boxstyle='round,pad=1', facecolor='lightgreen', alpha=0.8))
    
    plt.savefig('regression_equation.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("="*60)
    print("对Y浓度做对数变换的多元线性回归分析")
    print("="*60)
    
    # 加载数据
    df = load_and_analyze_data()
    
    # 进行对数变换回归分析
    model, X, log_Y, log_Y_pred, X_columns, y_column = log_transform_regression(df)
    
    # 创建可视化
    create_visualizations(model, X, log_Y, log_Y_pred, X_columns, y_column)
    
    print("\n分析完成！生成的文件:")
    print("- log_regression_analysis.png: 综合分析图表")
    print("- regression_equation.png: 回归方程展示")

if __name__ == "__main__":
    main()
