%% Y浓度对数变换的多元线性回归分析
% 作者: 数据分析团队
% 日期: 2025年1月
% 功能: 对Y染色体浓度进行对数变换并建立多元线性回归模型

clear; clc; close all;

% 配置中文字体支持（解决乱码问题）
if exist('setup_chinese_font.m', 'file')
    setup_chinese_font();
else
    % 备用字体设置
    try
        if ispc
            set(0,'DefaultAxesFontName','SimHei');
            set(0,'DefaultTextFontName','SimHei');
        elseif ismac
            set(0,'DefaultAxesFontName','PingFang SC');
            set(0,'DefaultTextFontName','PingFang SC');
        else
            set(0,'DefaultAxesFontName','DejaVu Sans');
            set(0,'DefaultTextFontName','DejaVu Sans');
        end
        set(0,'DefaultAxesFontSize',12);
        fprintf('已设置中文字体支持\n');
    catch
        fprintf('警告: 字体设置失败，可能出现中文乱码\n');
    end
end

fprintf('============================================================\n');
fprintf('Y浓度对数变换的多元线性回归分析\n');
fprintf('============================================================\n');

%% 1. 数据读取
try
    % 读取Excel文件
    data = readtable('999.xlsx', 'VariableNamingRule', 'preserve');
    fprintf('数据加载成功！\n');
    fprintf('数据维度: %d行 × %d列\n', height(data), width(data));

    % 显示前5行数据
    fprintf('\n数据前5行:\n');
    disp(data(1:5, :));

catch ME
    fprintf('读取Excel文件失败: %s\n', ME.message);
    fprintf('创建示例数据进行演示...\n');

    % 创建示例数据
    rng(42); % 设置随机种子
    n_samples = 100;

    % 生成示例数据
    age = normrnd(29, 3.6, n_samples, 1);
    height = normrnd(161, 5.1, n_samples, 1);
    weight = normrnd(83, 9.2, n_samples, 1);
    bmi = weight ./ (height/100).^2;
    gc_content = normrnd(0.42, 0.02, n_samples, 1);

    % 生成Y染色体浓度（确保为正值）
    y_concentration = exp(-2.5 + 0.01*age + 0.005*height - 0.003*weight + ...
                         2*gc_content + normrnd(0, 0.1, n_samples, 1));

    % 创建数据表（使用英文变量名避免兼容性问题）
    data = table(age, height, weight, bmi, gc_content, y_concentration, ...
                'VariableNames', {'Age', 'Height', 'Weight', 'BMI', 'GC_Content', 'Y_Concentration'});

    % 添加中文描述映射
    var_description = containers.Map(...
        {'Age', 'Height', 'Weight', 'BMI', 'GC_Content', 'Y_Concentration'}, ...
        {'年龄', '身高', '体重', '孕妇BMI', 'GC含量', 'Y染色体浓度'});

    fprintf('示例数据创建完成！\n');
    fprintf('数据维度: %d行 × %d列\n', height(data), width(data));
end

%% 2. 数据预处理
fprintf('\n正在进行数据预处理...\n');

% 识别数值型变量
numeric_vars = varfun(@isnumeric, data, 'output', 'uniform');
numeric_data = data(:, numeric_vars);

% 寻找Y浓度变量（支持中英文变量名）
y_var_candidates = {'Y染色体浓度', 'Y浓度百分比', 'Y染色体的Z值', 'Y_Concentration', 'Y_Density', 'Y_Chr_Concentration'};
y_var = '';

for i = 1:length(y_var_candidates)
    if any(strcmp(numeric_data.Properties.VariableNames, y_var_candidates{i}))
        y_var = y_var_candidates{i};
        break;
    end
end

% 如果没找到，使用包含'Y'的第一个数值变量
if isempty(y_var)
    var_names = numeric_data.Properties.VariableNames;
    for i = 1:length(var_names)
        if contains(upper(var_names{i}), 'Y')
            y_var = var_names{i};
            break;
        end
    end
end

% 如果还是没找到，使用最后一个变量
if isempty(y_var)
    y_var = numeric_data.Properties.VariableNames{end};
end

% 获取变量的中文描述（如果存在）
if exist('var_description', 'var') && isKey(var_description, y_var)
    y_var_display = var_description(y_var);
else
    y_var_display = y_var;
end

fprintf('使用 "%s" 作为因变量Y\n', y_var_display);

% 提取Y变量
Y = numeric_data.(y_var);

% 处理Y变量中的非正值
if any(Y <= 0)
    fprintf('警告: Y变量中有非正值，正在处理...\n');
    Y = Y + abs(min(Y)) + 1;
    fprintf('处理后Y的范围: [%.4f, %.4f]\n', min(Y), max(Y));
end

% 对Y进行对数变换
log_Y = log(Y);
fprintf('对数变换后log(Y)的范围: [%.4f, %.4f]\n', min(log_Y), max(log_Y));

% 获取自变量
x_vars = setdiff(numeric_data.Properties.VariableNames, {y_var});

% 移除缺失值过多的变量（超过50%）
valid_x_vars = {};
for i = 1:length(x_vars)
    var_data = numeric_data.(x_vars{i});
    missing_ratio = sum(isnan(var_data)) / length(var_data);
    if missing_ratio < 0.5
        valid_x_vars{end+1} = x_vars{i};
    end
end

fprintf('选择的自变量数量: %d\n', length(valid_x_vars));

% 构建自变量矩阵
X = [];
for i = 1:length(valid_x_vars)
    var_data = numeric_data.(valid_x_vars{i});
    % 用均值填充缺失值
    var_data(isnan(var_data)) = nanmean(var_data);
    X = [X, var_data];
end

% 添加常数项
X = [ones(size(X, 1), 1), X];
var_names = ['常数项', valid_x_vars];

% 创建变量名的中文显示映射
var_names_display = cell(size(var_names));
var_names_display{1} = '常数项';
for i = 2:length(var_names)
    if exist('var_description', 'var') && isKey(var_description, var_names{i})
        var_names_display{i} = var_description(var_names{i});
    else
        var_names_display{i} = var_names{i};
    end
end

fprintf('最终数据形状: X(%d×%d), Y(%d×1)\n', size(X), length(log_Y));

%% 3. 多元线性回归分析
fprintf('\n正在进行多元线性回归分析...\n');

% 计算回归系数
beta = (X' * X) \ (X' * log_Y);

% 预测值
log_Y_pred = X * beta;

% 计算评估指标
SS_res = sum((log_Y - log_Y_pred).^2);  % 残差平方和
SS_tot = sum((log_Y - mean(log_Y)).^2); % 总平方和
R_squared = 1 - SS_res / SS_tot;        % 决定系数
MSE = SS_res / (length(log_Y) - length(beta)); % 均方误差
RMSE = sqrt(MSE);                       % 均方根误差

% 显示结果
fprintf('\n============================================================\n');
fprintf('对数变换的多元线性回归结果\n');
fprintf('============================================================\n');

% 构建并显示回归方程
fprintf('\n回归方程:\n');
equation_str = sprintf('ln(Y) = %.4f', beta(1));
for i = 2:length(beta)
    if beta(i) >= 0
        equation_str = [equation_str, sprintf(' + %.4f×%s', beta(i), var_names{i})];
    else
        equation_str = [equation_str, sprintf(' %.4f×%s', beta(i), var_names{i})];
    end
end
fprintf('%s\n', equation_str);

fprintf('\n模型评估指标:\n');
fprintf('R² (决定系数): %.4f\n', R_squared);
fprintf('MSE (均方误差): %.4f\n', MSE);
fprintf('RMSE (均方根误差): %.4f\n', RMSE);

fprintf('\n回归系数详情:\n');
for i = 1:length(beta)
    fprintf('%s: %.4f\n', var_names_display{i}, beta(i));
end

%% 4. 可视化分析
fprintf('\n正在生成可视化图表...\n');

% 创建图形窗口
figure('Position', [100, 100, 1200, 800], 'Name', 'Y浓度对数变换多元线性回归分析');

% 1. 实际值vs预测值散点图
subplot(2, 3, 1);
scatter(log_Y, log_Y_pred, 50, 'b', 'filled', 'Alpha', 0.6);
hold on;
plot([min(log_Y), max(log_Y)], [min(log_Y), max(log_Y)], 'r--', 'LineWidth', 2);
xlabel('实际值 ln(Y)', 'FontSize', 12);
ylabel('预测值 ln(Y)', 'FontSize', 12);
title('实际值 vs 预测值', 'FontSize', 14, 'FontWeight', 'bold');
grid on;
% 添加R²信息
text(0.05, 0.95, sprintf('R² = %.4f', R_squared), 'Units', 'normalized', ...
     'BackgroundColor', 'white', 'EdgeColor', 'black', 'FontSize', 10);

% 2. 残差图
subplot(2, 3, 2);
residuals = log_Y - log_Y_pred;
scatter(log_Y_pred, residuals, 50, 'g', 'filled', 'Alpha', 0.6);
hold on;
yline(0, 'r--', 'LineWidth', 2);
xlabel('预测值 ln(Y)', 'FontSize', 12);
ylabel('残差', 'FontSize', 12);
title('残差图', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

% 3. 残差直方图
subplot(2, 3, 3);
histogram(residuals, 20, 'FaceColor', [1, 0.5, 0], 'EdgeColor', 'black', 'Alpha', 0.7);
xlabel('残差', 'FontSize', 12);
ylabel('频数', 'FontSize', 12);
title('残差分布直方图', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

% 4. Q-Q图（正态性检验）
subplot(2, 3, 4);
qqplot(residuals);
title('残差Q-Q图（正态性检验）', 'FontSize', 14, 'FontWeight', 'bold');
grid on;

% 5. 回归系数柱状图（排除常数项）
subplot(2, 3, 5);
coeffs = beta(2:end);  % 排除常数项
var_names_plot = var_names(2:end);

% 为正负系数设置不同颜色
colors = zeros(length(coeffs), 3);
for i = 1:length(coeffs)
    if coeffs(i) >= 0
        colors(i, :) = [0, 0, 1];  % 蓝色
    else
        colors(i, :) = [1, 0, 0];  % 红色
    end
end

bar_handle = bar(1:length(coeffs), coeffs, 'FaceColor', 'flat');
bar_handle.CData = colors;

% 添加数值标签
for i = 1:length(coeffs)
    if coeffs(i) >= 0
        text(i, coeffs(i) + 0.01*max(abs(coeffs)), sprintf('%.3f', coeffs(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 8);
    else
        text(i, coeffs(i) - 0.01*max(abs(coeffs)), sprintf('%.3f', coeffs(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'top', 'FontSize', 8);
    end
end

xlabel('自变量', 'FontSize', 12);
ylabel('回归系数', 'FontSize', 12);
title('回归系数', 'FontSize', 14, 'FontWeight', 'bold');
set(gca, 'XTick', 1:length(var_names_plot), 'XTickLabel', var_names_plot, 'XTickLabelRotation', 45);
grid on;

% 6. 相关性热力图
subplot(2, 3, 6);
% 创建包含log(Y)的数据矩阵用于相关性分析
corr_data = [X(:, 2:end), log_Y];  % 排除常数项
corr_matrix = corrcoef(corr_data);
corr_var_names = [var_names_plot, {'ln(Y)'}];

% 绘制热力图
imagesc(corr_matrix);
colorbar;
colormap('coolwarm');
caxis([-1, 1]);

% 添加数值标签
[m, n] = size(corr_matrix);
for i = 1:m
    for j = 1:n
        text(j, i, sprintf('%.2f', corr_matrix(i, j)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle', ...
             'FontSize', 8, 'Color', 'black');
    end
end

xlabel('变量', 'FontSize', 12);
ylabel('变量', 'FontSize', 12);
title('变量相关性热力图', 'FontSize', 14, 'FontWeight', 'bold');
set(gca, 'XTick', 1:length(corr_var_names), 'XTickLabel', corr_var_names, 'XTickLabelRotation', 45);
set(gca, 'YTick', 1:length(corr_var_names), 'YTickLabel', corr_var_names);

% 调整子图布局
sgtitle('Y浓度对数变换多元线性回归分析结果', 'FontSize', 16, 'FontWeight', 'bold');

% 保存图片
saveas(gcf, 'matlab_regression_analysis.png');
fprintf('图表已保存为: matlab_regression_analysis.png\n');

%% 5. 创建回归方程展示图
figure('Position', [200, 200, 1000, 600], 'Name', '回归方程展示');

% 清空坐标轴
axis off;

% 标题
text(0.5, 0.9, 'Y浓度对数变换多元线性回归方程', ...
     'HorizontalAlignment', 'center', 'FontSize', 20, 'FontWeight', 'bold');

% 回归方程（分行显示以避免过长）
equation_lines = {};
current_line = sprintf('ln(Y) = %.4f', beta(1));
line_length = length(current_line);

for i = 2:length(beta)
    term = '';
    if beta(i) >= 0
        term = sprintf(' + %.4f×%s', beta(i), var_names{i});
    else
        term = sprintf(' %.4f×%s', beta(i), var_names{i});
    end

    % 检查是否需要换行（每行最多80个字符）
    if line_length + length(term) > 80
        equation_lines{end+1} = current_line;
        current_line = ['        ' term];  % 缩进
        line_length = length(current_line);
    else
        current_line = [current_line term];
        line_length = line_length + length(term);
    end
end
equation_lines{end+1} = current_line;

% 显示回归方程
y_pos = 0.7;
for i = 1:length(equation_lines)
    text(0.5, y_pos - (i-1)*0.08, equation_lines{i}, ...
         'HorizontalAlignment', 'center', 'FontSize', 12, ...
         'BackgroundColor', [0.9, 0.9, 1], 'EdgeColor', 'black', 'Margin', 5);
end

% 模型评估指标
metrics_text = {
    sprintf('模型评估指标:');
    sprintf('R² (决定系数): %.4f', R_squared);
    sprintf('MSE (均方误差): %.4f', MSE);
    sprintf('RMSE (均方根误差): %.4f', RMSE);
    sprintf('样本数量: %d', length(log_Y));
    sprintf('自变量数量: %d', length(valid_x_vars));
};

y_pos = 0.35;
for i = 1:length(metrics_text)
    text(0.5, y_pos - (i-1)*0.05, metrics_text{i}, ...
         'HorizontalAlignment', 'center', 'FontSize', 14, ...
         'BackgroundColor', [0.9, 1, 0.9], 'EdgeColor', 'black', 'Margin', 5);
end

% 保存回归方程图
saveas(gcf, 'matlab_regression_equation.png');
fprintf('回归方程图已保存为: matlab_regression_equation.png\n');

%% 6. 模型诊断和统计检验
fprintf('\n正在进行模型诊断...\n');

% 计算标准误差
n = length(log_Y);
p = length(beta);
df = n - p;  % 自由度

% 计算回归系数的标准误差
X_inv = inv(X' * X);
se_beta = sqrt(diag(X_inv) * MSE);

% 计算t统计量和p值
t_stats = beta ./ se_beta;
p_values = 2 * (1 - tcdf(abs(t_stats), df));

% 显示统计检验结果
fprintf('\n回归系数统计检验:\n');
fprintf('%-20s %10s %10s %10s %10s\n', '变量', '系数', '标准误差', 't统计量', 'p值');
fprintf('%-20s %10s %10s %10s %10s\n', repmat('-', 1, 20), repmat('-', 1, 10), ...
        repmat('-', 1, 10), repmat('-', 1, 10), repmat('-', 1, 10));
for i = 1:length(beta)
    significance = '';
    if p_values(i) < 0.001
        significance = '***';
    elseif p_values(i) < 0.01
        significance = '**';
    elseif p_values(i) < 0.05
        significance = '*';
    end

    fprintf('%-20s %10.4f %10.4f %10.4f %10.4f %s\n', ...
            var_names{i}, beta(i), se_beta(i), t_stats(i), p_values(i), significance);
end

fprintf('\n显著性水平: *** p<0.001, ** p<0.01, * p<0.05\n');

% 计算置信区间
alpha = 0.05;  % 95%置信区间
t_critical = tinv(1 - alpha/2, df);
ci_lower = beta - t_critical * se_beta;
ci_upper = beta + t_critical * se_beta;

fprintf('\n回归系数95%%置信区间:\n');
fprintf('%-20s %15s %15s\n', '变量', '下限', '上限');
fprintf('%-20s %15s %15s\n', repmat('-', 1, 20), repmat('-', 1, 15), repmat('-', 1, 15));
for i = 1:length(beta)
    fprintf('%-20s %15.4f %15.4f\n', var_names{i}, ci_lower(i), ci_upper(i));
end

%% 7. 总结报告
fprintf('\n============================================================\n');
fprintf('分析总结\n');
fprintf('============================================================\n');

fprintf('\n主要发现:\n');
fprintf('1. 模型拟合效果: R² = %.4f，模型解释了%.1f%%的Y染色体浓度变异\n', ...
        R_squared, R_squared * 100);

% 找出最重要的影响因子（按系数绝对值排序）
[~, sorted_idx] = sort(abs(beta(2:end)), 'descend');  % 排除常数项
fprintf('2. 最重要的影响因子（按影响程度排序）:\n');
for i = 1:min(5, length(sorted_idx))  % 显示前5个最重要的因子
    idx = sorted_idx(i) + 1;  % 加1因为排除了常数项
    fprintf('   %s: %.4f\n', var_names{idx}, beta(idx));
end

fprintf('\n技术指标:\n');
fprintf('- 均方根误差(RMSE): %.4f\n', RMSE);
fprintf('- 样本数量: %d\n', n);
fprintf('- 自变量数量: %d\n', length(valid_x_vars));

fprintf('\n生成的文件:\n');
fprintf('- matlab_regression_analysis.png: 综合分析图表\n');
fprintf('- matlab_regression_equation.png: 回归方程展示\n');

fprintf('\n============================================================\n');
fprintf('分析完成！\n');
fprintf('============================================================\n');