%% MATLAB Chinese Font Configuration Script
%% MATLAB中文字体配置脚本 - 彻底解决乱码问题

function setup_chinese_font()
    fprintf('Configuring Chinese font support for MATLAB...\n');
    fprintf('正在为MATLAB配置中文字体支持...\n');

    % Detect operating system - 检测操作系统
    if ispc
        % Windows system - Windows系统
        chinese_fonts = {'Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong'};
        fprintf('Windows system detected.\n');
        fprintf('检测到Windows系统。\n');

        % Additional Windows-specific settings
        try
            % Set system locale for better Chinese support
            system('chcp 65001 >nul 2>&1');  % Set to UTF-8
        catch
            % Ignore if command fails
        end

    elseif ismac
        % macOS system - macOS系统
        chinese_fonts = {'PingFang SC', 'Heiti SC', 'STHeiti', 'STKaiti', 'STSong'};
        fprintf('macOS system detected.\n');
        fprintf('检测到macOS系统。\n');
    else
        % Linux system - Linux系统
        chinese_fonts = {'WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'DejaVu Sans', 'Liberation Sans'};
        fprintf('Linux system detected.\n');
        fprintf('检测到Linux系统。\n');
    end

    % Try to set Chinese fonts - 尝试设置中文字体
    font_set = false;
    successful_font = '';

    for i = 1:length(chinese_fonts)
        try
            % Set all default fonts - 设置所有默认字体
            set(0, 'DefaultAxesFontName', chinese_fonts{i});
            set(0, 'DefaultTextFontName', chinese_fonts{i});
            set(0, 'DefaultUicontrolFontName', chinese_fonts{i});
            set(0, 'DefaultUipanelFontName', chinese_fonts{i});
            set(0, 'DefaultUitableFontName', chinese_fonts{i});

            % Set font sizes - 设置字体大小
            set(0, 'DefaultAxesFontSize', 12);
            set(0, 'DefaultTextFontSize', 12);
            set(0, 'DefaultUicontrolFontSize', 10);
            set(0, 'DefaultUitableFontSize', 10);

            % Set font weight and angle - 设置字体粗细和角度
            set(0, 'DefaultAxesFontWeight', 'normal');
            set(0, 'DefaultTextFontWeight', 'normal');

            successful_font = chinese_fonts{i};
            font_set = true;
            fprintf('Successfully set Chinese font: %s\n', chinese_fonts{i});
            fprintf('成功设置中文字体：%s\n', chinese_fonts{i});
            break;

        catch ME
            fprintf('Failed to set font %s: %s\n', chinese_fonts{i}, ME.message);
            fprintf('设置字体 %s 失败：%s\n', chinese_fonts{i}, ME.message);
        end
    end

    if ~font_set
        fprintf('Warning: Unable to set Chinese fonts, Chinese characters may appear as squares.\n');
        fprintf('警告：无法设置中文字体，中文字符可能显示为方框。\n');
        fprintf('Recommendation: Use English labels or manually install Chinese fonts.\n');
        fprintf('建议：使用英文标签或手动安装中文字体。\n');
    end

    % Set character encoding - 设置字符编码
    try
        feature('DefaultCharacterSet', 'UTF-8');
        fprintf('Character encoding set to UTF-8.\n');
        fprintf('字符编码已设置为UTF-8。\n');
    catch ME
        fprintf('Warning: Unable to set UTF-8 encoding: %s\n', ME.message);
        fprintf('警告：无法设置UTF-8编码：%s\n', ME.message);

        % Try alternative encoding settings
        try
            feature('DefaultCharacterSet', 'GBK');
            fprintf('Fallback: Character encoding set to GBK.\n');
            fprintf('备选方案：字符编码已设置为GBK。\n');
        catch
            fprintf('Warning: Unable to set any character encoding.\n');
            fprintf('警告：无法设置任何字符编码。\n');
        end
    end

    % Test Chinese display - 测试中文显示
    fprintf('\nTesting Chinese character display...\n');
    fprintf('测试中文字符显示...\n');

    test_figure = figure('Visible', 'off', 'Position', [100, 100, 400, 300]);
    try
        % Create text with Chinese characters
        h_text = text(0.5, 0.5, 'Chinese Test: Y染色体浓度分析', ...
                     'FontSize', 14, 'HorizontalAlignment', 'center', ...
                     'Units', 'normalized');

        if font_set
            set(h_text, 'FontName', successful_font);
        end

        drawnow;
        fprintf('Chinese character display test passed.\n');
        fprintf('中文字符显示测试通过。\n');

    catch ME
        fprintf('Chinese character display test failed: %s\n', ME.message);
        fprintf('中文字符显示测试失败：%s\n', ME.message);
    end

    close(test_figure);

    % Additional recommendations - 额外建议
    fprintf('\nAdditional recommendations for Chinese display:\n');
    fprintf('中文显示的额外建议：\n');
    fprintf('1. Restart MATLAB after font configuration\n');
    fprintf('   字体配置后重启MATLAB\n');
    fprintf('2. Use English labels in plots if Chinese characters still appear as squares\n');
    fprintf('   如果中文字符仍显示为方框，请在图表中使用英文标签\n');
    fprintf('3. Check system font installation\n');
    fprintf('   检查系统字体安装\n');

    fprintf('\nChinese font configuration completed!\n');
    fprintf('中文字体配置完成！\n\n');
end
