%% MATLAB中文字体配置脚本
% 解决MATLAB中文显示乱码问题

function setup_chinese_font()
    fprintf('正在配置MATLAB中文字体支持...\n');
    
    % 检测操作系统
    if ispc
        % Windows系统
        chinese_fonts = {'SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi'};
        fprintf('检测到Windows系统\n');
    elseif ismac
        % macOS系统
        chinese_fonts = {'PingFang SC', 'Heiti SC', 'STHeiti', 'STKaiti'};
        fprintf('检测到macOS系统\n');
    else
        % Linux系统
        chinese_fonts = {'WenQuanYi Micro Hei', 'DejaVu Sans', 'Liberation Sans'};
        fprintf('检测到Linux系统\n');
    end
    
    % 尝试设置中文字体
    font_set = false;
    for i = 1:length(chinese_fonts)
        try
            % 设置默认字体
            set(0, 'DefaultAxesFontName', chinese_fonts{i});
            set(0, 'DefaultTextFontName', chinese_fonts{i});
            set(0, 'DefaultUicontrolFontName', chinese_fonts{i});
            set(0, 'DefaultUipanelFontName', chinese_fonts{i});
            
            % 设置字体大小
            set(0, 'DefaultAxesFontSize', 12);
            set(0, 'DefaultTextFontSize', 12);
            set(0, 'DefaultUicontrolFontSize', 10);
            
            fprintf('成功设置中文字体: %s\n', chinese_fonts{i});
            font_set = true;
            break;
        catch
            fprintf('尝试字体 %s 失败\n', chinese_fonts{i});
        end
    end
    
    if ~font_set
        fprintf('警告: 无法设置中文字体，可能会出现乱码\n');
        fprintf('建议手动安装中文字体或使用英文标签\n');
    end
    
    % 设置字符编码
    try
        feature('DefaultCharacterSet', 'UTF-8');
        fprintf('字符编码设置为UTF-8\n');
    catch
        fprintf('警告: 无法设置UTF-8编码\n');
    end
    
    % 测试中文显示
    fprintf('\n测试中文显示:\n');
    test_figure = figure('Visible', 'off');
    try
        text(0.5, 0.5, '中文测试：Y染色体浓度分析', 'FontSize', 14);
        fprintf('中文显示测试通过\n');
    catch ME
        fprintf('中文显示测试失败: %s\n', ME.message);
    end
    close(test_figure);
    
    fprintf('中文字体配置完成！\n\n');
end
