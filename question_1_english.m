%% Y Concentration Log-Transform Multiple Linear Regression Analysis
% Complete English version to avoid any encoding issues
% Author: Data Analysis Team
% Date: January 2025

clear; clc; close all;

% Set font for better display
if ispc
    set(0,'DefaultAxesFontName','Arial');
    set(0,'DefaultTextFontName','Arial');
elseif ismac
    set(0,'DefaultAxesFontName','Helvetica');
    set(0,'DefaultTextFontName','Helvetica');
else
    set(0,'DefaultAxesFontName','DejaVu Sans');
    set(0,'DefaultTextFontName','DejaVu Sans');
end
set(0,'DefaultAxesFontSize',12);

fprintf('============================================================\n');
fprintf('Y Concentration Log-Transform Multiple Linear Regression\n');
fprintf('============================================================\n');

%% 1. Data Loading
try
    % Read Excel file
    data = readtable('999.xlsx');
    fprintf('Data loaded successfully!\n');
    fprintf('Data dimensions: %d rows x %d columns\n', height(data), width(data));
    
    % Display first few variable names
    fprintf('\nFirst 10 variable names:\n');
    var_names = data.Properties.VariableNames;
    for i = 1:min(10, length(var_names))
        fprintf('%d. %s\n', i, var_names{i});
    end
    if length(var_names) > 10
        fprintf('... and %d more variables\n', length(var_names) - 10);
    end
    
catch ME
    fprintf('Failed to read Excel file: %s\n', ME.message);
    fprintf('Creating sample data for demonstration...\n');
    
    % Create sample data
    rng(42);
    n = 100;
    
    age = normrnd(29, 3.6, n, 1);
    height = normrnd(161, 5.1, n, 1);
    weight = normrnd(83, 9.2, n, 1);
    bmi = weight ./ (height/100).^2;
    gc_content = normrnd(0.42, 0.02, n, 1);
    y_concentration = exp(-2.5 + 0.01*age + 0.005*height - 0.003*weight + ...
                         2*gc_content + normrnd(0, 0.1, n, 1));
    
    data = table(age, height, weight, bmi, gc_content, y_concentration, ...
                'VariableNames', {'Age', 'Height', 'Weight', 'BMI', 'GC_Content', 'Y_Concentration'});
    
    fprintf('Sample data created successfully!\n');
    fprintf('Data dimensions: %d rows x %d columns\n', height(data), width(data));
end

%% 2. Data Preprocessing
fprintf('\nData preprocessing...\n');

% Get numeric variables
numeric_vars = varfun(@isnumeric, data, 'output', 'uniform');
numeric_data = data(:, numeric_vars);
all_var_names = numeric_data.Properties.VariableNames;

% Find Y variable (Y concentration related)
y_var = '';
y_patterns = {'Y_Concentration', 'Y____', 'y_concentration', 'Y_Chr'};

for pattern = y_patterns
    for i = 1:length(all_var_names)
        if contains(all_var_names{i}, pattern{1})
            y_var = all_var_names{i};
            break;
        end
    end
    if ~isempty(y_var), break; end
end

% If not found, look for variables containing 'Y'
if isempty(y_var)
    for i = 1:length(all_var_names)
        if contains(upper(all_var_names{i}), 'Y')
            y_var = all_var_names{i};
            break;
        end
    end
end

% If still not found, use the last variable
if isempty(y_var)
    y_var = all_var_names{end};
end

fprintf('Selected Y variable: %s\n', y_var);

% Extract Y data
Y = numeric_data.(y_var);

% Handle non-positive values
if any(Y <= 0)
    fprintf('Warning: Non-positive values detected in Y, adjusting...\n');
    Y = Y + abs(min(Y)) + 1;
    fprintf('Y range after adjustment: [%.4f, %.4f]\n', min(Y), max(Y));
end

% Log transformation
log_Y = log(Y);
fprintf('Log-transformed Y range: [%.4f, %.4f]\n', min(log_Y), max(log_Y));

% Select independent variables
x_vars = setdiff(all_var_names, {y_var});
valid_x_vars = {};

fprintf('Selecting variables with <30%% missing values...\n');

for i = 1:length(x_vars)
    var_data = numeric_data.(x_vars{i});
    missing_ratio = sum(isnan(var_data)) / length(var_data);
    if missing_ratio < 0.3
        valid_x_vars{end+1} = x_vars{i};
    end
end

% Limit number of variables to avoid overfitting
max_vars = min(8, floor(length(log_Y)/15));
if length(valid_x_vars) > max_vars
    fprintf('Too many variables (%d), selecting top %d based on correlation...\n', ...
            length(valid_x_vars), max_vars);
    
    % Calculate correlations with Y
    correlations = zeros(length(valid_x_vars), 1);
    for i = 1:length(valid_x_vars)
        var_data = numeric_data.(valid_x_vars{i});
        var_data(isnan(var_data)) = nanmean(var_data);
        correlations(i) = abs(corr(var_data, log_Y, 'rows', 'complete'));
    end
    
    [~, sorted_idx] = sort(correlations, 'descend');
    valid_x_vars = valid_x_vars(sorted_idx(1:max_vars));
end

fprintf('Selected %d independent variables\n', length(valid_x_vars));

% Build design matrix
X = ones(length(log_Y), 1);  % Intercept term
var_names_full = {'Intercept'};

for i = 1:length(valid_x_vars)
    var_data = numeric_data.(valid_x_vars{i});
    % Fill missing values with mean
    var_data(isnan(var_data)) = nanmean(var_data);
    % Standardize to reduce numerical issues
    var_data = (var_data - mean(var_data)) / std(var_data);
    X = [X, var_data];
    var_names_full{end+1} = valid_x_vars{i};
end

fprintf('Design matrix dimensions: %dx%d\n', size(X));

%% 3. Regression Analysis
fprintf('\nPerforming regression analysis...\n');

% Check condition number
cond_num = cond(X'*X);
fprintf('Matrix condition number: %.2e\n', cond_num);

if cond_num > 1e12
    fprintf('Warning: Matrix is ill-conditioned, using ridge regression...\n');
    lambda = 0.01;
    beta = (X'*X + lambda*eye(size(X,2))) \ (X'*log_Y);
else
    % Ordinary least squares
    beta = (X'*X) \ (X'*log_Y);
end

% Predictions and evaluation
log_Y_pred = X * beta;
residuals = log_Y - log_Y_pred;

SS_res = sum(residuals.^2);
SS_tot = sum((log_Y - mean(log_Y)).^2);
R_squared = 1 - SS_res / SS_tot;
MSE = SS_res / (length(log_Y) - length(beta));
RMSE = sqrt(MSE);

%% 4. Results Display
fprintf('\n============================================================\n');
fprintf('REGRESSION RESULTS\n');
fprintf('============================================================\n');

fprintf('\nRegression Equation:\n');
eq_str = sprintf('ln(Y) = %.4f', beta(1));
for i = 2:length(beta)
    if beta(i) >= 0
        eq_str = [eq_str, sprintf(' + %.4f*%s', beta(i), var_names_full{i})];
    else
        eq_str = [eq_str, sprintf(' %.4f*%s', beta(i), var_names_full{i})];
    end
end
fprintf('%s\n', eq_str);

fprintf('\nModel Performance:\n');
fprintf('R-squared: %.4f\n', R_squared);
fprintf('RMSE: %.4f\n', RMSE);
fprintf('Sample size: %d\n', length(log_Y));

fprintf('\nRegression Coefficients:\n');
for i = 1:length(beta)
    fprintf('%-20s: %8.4f\n', var_names_full{i}, beta(i));
end

%% 5. Visualization
fprintf('\nGenerating visualizations...\n');

figure('Position', [100, 100, 1200, 800], 'Name', 'Y Concentration Regression Analysis');

% 1. Actual vs Predicted
subplot(2, 3, 1);
scatter(log_Y, log_Y_pred, 30, 'b', 'filled');
hold on;
plot([min(log_Y), max(log_Y)], [min(log_Y), max(log_Y)], 'r--', 'LineWidth', 2);
xlabel('Actual ln(Y)');
ylabel('Predicted ln(Y)');
title('Actual vs Predicted Values');
grid on;
text(0.05, 0.95, sprintf('R^2 = %.3f', R_squared), 'Units', 'normalized', ...
     'BackgroundColor', 'white', 'FontSize', 10);

% 2. Residual plot
subplot(2, 3, 2);
scatter(log_Y_pred, residuals, 30, 'g', 'filled');
hold on;
plot([min(log_Y_pred), max(log_Y_pred)], [0, 0], 'r--', 'LineWidth', 2);
xlabel('Predicted ln(Y)');
ylabel('Residuals');
title('Residual Plot');
grid on;

% 3. Residual histogram
subplot(2, 3, 3);
hist(residuals, 15);
xlabel('Residuals');
ylabel('Frequency');
title('Residual Distribution');
grid on;

% 4. Q-Q plot
subplot(2, 3, 4);
try
    qqplot(residuals);
    title('Q-Q Plot (Normality Test)');
catch
    plot(sort(residuals), 'o-');
    xlabel('Sample Index');
    ylabel('Sorted Residuals');
    title('Sorted Residuals Plot');
end
grid on;

% 5. Coefficient plot
subplot(2, 3, 5);
coeffs = beta(2:end);  % Exclude intercept
bar(coeffs);
xlabel('Variable Index');
ylabel('Coefficient Value');
title('Regression Coefficients');
grid on;

% 6. Fitted values plot
subplot(2, 3, 6);
plot(1:length(log_Y), log_Y, 'bo-', 'MarkerSize', 3, 'LineWidth', 1);
hold on;
plot(1:length(log_Y), log_Y_pred, 'r.-', 'MarkerSize', 3, 'LineWidth', 1);
xlabel('Sample Index');
ylabel('ln(Y) Value');
title('Actual vs Fitted Values');
legend('Actual', 'Fitted', 'Location', 'best');
grid on;

sgtitle('Y Concentration Log-Transform Multiple Linear Regression Analysis');

% Save figure
try
    saveas(gcf, 'matlab_english_analysis.png');
    fprintf('Figure saved as: matlab_english_analysis.png\n');
catch
    fprintf('Warning: Could not save figure\n');
end

%% 6. Save Results
fprintf('\nSaving results...\n');

try
    results_table = table(var_names_full', beta, 'VariableNames', {'Variable', 'Coefficient'});
    writetable(results_table, 'matlab_english_results.csv');
    fprintf('Results saved to: matlab_english_results.csv\n');
catch
    fprintf('Warning: Could not save CSV file\n');
end

fprintf('\n============================================================\n');
fprintf('ANALYSIS SUMMARY\n');
fprintf('============================================================\n');
fprintf('Model Performance:\n');
fprintf('- R-squared: %.4f (explains %.1f%% of variance)\n', R_squared, R_squared*100);
fprintf('- RMSE: %.4f\n', RMSE);
fprintf('- Sample size: %d\n', length(log_Y));
fprintf('- Number of predictors: %d\n', length(valid_x_vars));

fprintf('\nTop 3 Most Important Variables:\n');
[~, sorted_idx] = sort(abs(beta(2:end)), 'descend');
for i = 1:min(3, length(sorted_idx))
    idx = sorted_idx(i) + 1;
    fprintf('%d. %s: %.4f\n', i, var_names_full{idx}, beta(idx));
end

fprintf('\nFiles Generated:\n');
fprintf('- matlab_english_analysis.png: Comprehensive analysis plots\n');
fprintf('- matlab_english_results.csv: Regression coefficients table\n');

fprintf('\n============================================================\n');
fprintf('ANALYSIS COMPLETED SUCCESSFULLY!\n');
fprintf('============================================================\n');
