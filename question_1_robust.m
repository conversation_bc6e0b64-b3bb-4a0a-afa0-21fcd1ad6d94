%% Y浓度对数变换的多元线性回归分析 (稳健版)
% 解决多重共线性和兼容性问题

clear; clc; close all;

% 设置字体
if ispc
    set(0,'DefaultAxesFontName','SimHei');
    set(0,'DefaultTextFontName','SimHei');
end
set(0,'DefaultAxesFontSize',12);

fprintf('============================================================\n');
fprintf('Y Concentration Log-Transform Regression Analysis\n');
fprintf('============================================================\n');

%% 1. 数据读取
try
    % 读取Excel文件
    data = readtable('999.xlsx');
    fprintf('Data loaded successfully!\n');
    fprintf('Dimensions: %d rows x %d columns\n', height(data), width(data));
    
catch ME
    fprintf('Excel read failed: %s\n', ME.message);
    fprintf('Creating sample data...\n');
    
    % 创建示例数据
    rng(42);
    n = 100;
    
    age = normrnd(29, 3.6, n, 1);
    height = normrnd(161, 5.1, n, 1);
    weight = normrnd(83, 9.2, n, 1);
    bmi = weight ./ (height/100).^2;
    gc_content = normrnd(0.42, 0.02, n, 1);
    y_conc = exp(-2.5 + 0.01*age + 0.005*height - 0.003*weight + ...
                 2*gc_content + normrnd(0, 0.1, n, 1));
    
    data = table(age, height, weight, bmi, gc_content, y_conc);
    fprintf('Sample data created: %d rows x %d columns\n', height(data), width(data));
end

%% 2. 数据预处理
fprintf('\nData preprocessing...\n');

% 获取数值型变量
numeric_vars = varfun(@isnumeric, data, 'output', 'uniform');
numeric_data = data(:, numeric_vars);
var_names = numeric_data.Properties.VariableNames;

% 寻找Y变量（优先级：Y浓度 > Y相关 > 最后一个）
y_var = '';
y_patterns = {'Y', 'y_conc', 'concentration', 'density'};

for pattern = y_patterns
    for i = 1:length(var_names)
        if contains(upper(var_names{i}), upper(pattern{1}))
            y_var = var_names{i};
            break;
        end
    end
    if ~isempty(y_var), break; end
end

if isempty(y_var)
    y_var = var_names{end};
end

fprintf('Y variable: %s\n', y_var);

% 提取Y数据
Y = numeric_data.(y_var);

% 处理非正值
if any(Y <= 0)
    fprintf('Adjusting non-positive Y values...\n');
    Y = Y + abs(min(Y)) + 1;
end

% 对数变换
log_Y = log(Y);
fprintf('Log(Y) range: [%.4f, %.4f]\n', min(log_Y), max(log_Y));

% 选择自变量（移除Y变量和缺失值过多的变量）
x_vars = setdiff(var_names, {y_var});
valid_x_vars = {};

for i = 1:length(x_vars)
    var_data = numeric_data.(x_vars{i});
    missing_ratio = sum(isnan(var_data)) / length(var_data);
    if missing_ratio < 0.3  % 更严格的缺失值阈值
        valid_x_vars{end+1} = x_vars{i};
    end
end

% 限制变量数量以避免过拟合
max_vars = min(10, floor(length(log_Y)/10));  % 样本数的1/10或最多10个变量
if length(valid_x_vars) > max_vars
    fprintf('Too many variables (%d), selecting top %d...\n', length(valid_x_vars), max_vars);
    
    % 计算与Y的相关性，选择最相关的变量
    correlations = zeros(length(valid_x_vars), 1);
    for i = 1:length(valid_x_vars)
        var_data = numeric_data.(valid_x_vars{i});
        var_data(isnan(var_data)) = nanmean(var_data);
        correlations(i) = abs(corr(var_data, log_Y, 'rows', 'complete'));
    end
    
    [~, sorted_idx] = sort(correlations, 'descend');
    valid_x_vars = valid_x_vars(sorted_idx(1:max_vars));
end

fprintf('Selected %d variables\n', length(valid_x_vars));

% 构建设计矩阵
X = ones(length(log_Y), 1);  % 常数项
var_names_full = {'Intercept'};

for i = 1:length(valid_x_vars)
    var_data = numeric_data.(valid_x_vars{i});
    % 填充缺失值
    var_data(isnan(var_data)) = nanmean(var_data);
    % 标准化以减少数值问题
    var_data = (var_data - mean(var_data)) / std(var_data);
    X = [X, var_data];
    var_names_full{end+1} = valid_x_vars{i};
end

fprintf('Design matrix: %dx%d\n', size(X));

%% 3. 回归分析
fprintf('\nPerforming regression...\n');

% 检查条件数
cond_num = cond(X'*X);
fprintf('Condition number: %.2e\n', cond_num);

if cond_num > 1e12
    fprintf('Warning: Matrix is ill-conditioned, using regularization...\n');
    % 使用岭回归
    lambda = 0.01;
    beta = (X'*X + lambda*eye(size(X,2))) \ (X'*log_Y);
else
    % 普通最小二乘
    beta = (X'*X) \ (X'*log_Y);
end

% 预测和评估
log_Y_pred = X * beta;
residuals = log_Y - log_Y_pred;

SS_res = sum(residuals.^2);
SS_tot = sum((log_Y - mean(log_Y)).^2);
R_squared = 1 - SS_res / SS_tot;
MSE = SS_res / (length(log_Y) - length(beta));
RMSE = sqrt(MSE);

%% 4. 结果显示
fprintf('\n============================================================\n');
fprintf('REGRESSION RESULTS\n');
fprintf('============================================================\n');

fprintf('\nRegression Equation:\n');
eq_str = sprintf('ln(Y) = %.4f', beta(1));
for i = 2:length(beta)
    if beta(i) >= 0
        eq_str = [eq_str, sprintf(' + %.4f*%s', beta(i), var_names_full{i})];
    else
        eq_str = [eq_str, sprintf(' %.4f*%s', beta(i), var_names_full{i})];
    end
end
fprintf('%s\n', eq_str);

fprintf('\nModel Performance:\n');
fprintf('R-squared: %.4f\n', R_squared);
fprintf('RMSE: %.4f\n', RMSE);
fprintf('Sample size: %d\n', length(log_Y));

fprintf('\nCoefficients:\n');
for i = 1:length(beta)
    fprintf('%-15s: %8.4f\n', var_names_full{i}, beta(i));
end

%% 5. 可视化
fprintf('\nGenerating plots...\n');

figure('Position', [100, 100, 1200, 800]);

% 1. 实际vs预测
subplot(2, 3, 1);
scatter(log_Y, log_Y_pred, 30, 'b', 'filled');
hold on;
plot([min(log_Y), max(log_Y)], [min(log_Y), max(log_Y)], 'r--', 'LineWidth', 2);
xlabel('Actual ln(Y)');
ylabel('Predicted ln(Y)');
title('Actual vs Predicted');
grid on;
text(0.05, 0.95, sprintf('R² = %.3f', R_squared), 'Units', 'normalized', ...
     'BackgroundColor', 'white');

% 2. 残差图
subplot(2, 3, 2);
scatter(log_Y_pred, residuals, 30, 'g', 'filled');
hold on;
plot([min(log_Y_pred), max(log_Y_pred)], [0, 0], 'r--');
xlabel('Predicted ln(Y)');
ylabel('Residuals');
title('Residual Plot');
grid on;

% 3. 残差直方图
subplot(2, 3, 3);
hist(residuals, 15);
xlabel('Residuals');
ylabel('Frequency');
title('Residual Distribution');
grid on;

% 4. Q-Q图
subplot(2, 3, 4);
qqplot(residuals);
title('Q-Q Plot');
grid on;

% 5. 系数图
subplot(2, 3, 5);
coeffs = beta(2:end);
bar(coeffs);
xlabel('Variable Index');
ylabel('Coefficient');
title('Regression Coefficients');
grid on;

% 6. 拟合图
subplot(2, 3, 6);
plot(1:length(log_Y), log_Y, 'bo-', 'MarkerSize', 4);
hold on;
plot(1:length(log_Y), log_Y_pred, 'r.-', 'MarkerSize', 4);
xlabel('Sample Index');
ylabel('ln(Y)');
title('Fitted Values');
legend('Actual', 'Predicted', 'Location', 'best');
grid on;

sgtitle('Y Concentration Log-Transform Regression Analysis');

% 保存图片
saveas(gcf, 'matlab_robust_analysis.png');
fprintf('Plot saved: matlab_robust_analysis.png\n');

%% 6. 保存结果
fprintf('\nSaving results...\n');

% 创建结果表
results_table = table(var_names_full', beta, 'VariableNames', {'Variable', 'Coefficient'});

% 保存到文件
writetable(results_table, 'regression_results.csv');
fprintf('Results saved: regression_results.csv\n');

fprintf('\n============================================================\n');
fprintf('Analysis completed successfully!\n');
fprintf('============================================================\n');
