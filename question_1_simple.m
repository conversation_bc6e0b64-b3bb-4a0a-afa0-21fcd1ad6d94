%% Y浓度对数变换的多元线性回归分析 (简化版)
% 解决中文编码问题的简化版本

clear; clc; close all;

% 设置字体（避免中文乱码）
if ispc
    set(0,'DefaultAxesFontName','SimHei');
    set(0,'DefaultTextFontName','SimHei');
elseif ismac
    set(0,'DefaultAxesFontName','PingFang SC');
    set(0,'DefaultTextFontName','PingFang SC');
end
set(0,'DefaultAxesFontSize',12);

fprintf('============================================================\n');
fprintf('Y Concentration Log-Transform Multiple Linear Regression Analysis\n');
fprintf('Y浓度对数变换多元线性回归分析\n');
fprintf('============================================================\n');

%% 1. 数据读取
try
    % 读取Excel文件
    data = readtable('999.xlsx');
    fprintf('Data loaded successfully!\n');
    fprintf('Data dimensions: %d rows x %d columns\n', height(data), width(data));
    
    % 显示变量名
    fprintf('\nVariable names:\n');
    disp(data.Properties.VariableNames');
    
catch ME
    fprintf('Failed to read Excel file: %s\n', ME.message);
    fprintf('Creating sample data for demonstration...\n');
    
    % 创建示例数据
    rng(42);
    n_samples = 100;
    
    % 生成示例数据
    age = normrnd(29, 3.6, n_samples, 1);
    height = normrnd(161, 5.1, n_samples, 1);
    weight = normrnd(83, 9.2, n_samples, 1);
    bmi = weight ./ (height/100).^2;
    gc_content = normrnd(0.42, 0.02, n_samples, 1);
    
    % 生成Y染色体浓度
    y_concentration = exp(-2.5 + 0.01*age + 0.005*height - 0.003*weight + ...
                         2*gc_content + normrnd(0, 0.1, n_samples, 1));
    
    % 创建数据表
    data = table(age, height, weight, bmi, gc_content, y_concentration);
    
    fprintf('Sample data created!\n');
    fprintf('Data dimensions: %d rows x %d columns\n', height(data), width(data));
end

%% 2. 数据预处理
fprintf('\nData preprocessing...\n');

% 识别数值型变量
numeric_vars = varfun(@isnumeric, data, 'output', 'uniform');
numeric_data = data(:, numeric_vars);

% 寻找Y浓度变量
var_names = numeric_data.Properties.VariableNames;
y_var = '';

% 寻找包含Y或concentration的变量
for i = 1:length(var_names)
    var_name_upper = upper(var_names{i});
    if contains(var_name_upper, 'Y') && (contains(var_name_upper, 'CONCENTRATION') || ...
       contains(var_name_upper, 'DENSITY') || contains(var_name_upper, 'CHR'))
        y_var = var_names{i};
        break;
    end
end

% 如果没找到，使用最后一个变量
if isempty(y_var)
    y_var = var_names{end};
end

fprintf('Using "%s" as dependent variable Y\n', y_var);

% 提取Y变量
Y = numeric_data.(y_var);

% 处理Y变量中的非正值
if any(Y <= 0)
    fprintf('Warning: Non-positive values in Y, adjusting...\n');
    Y = Y + abs(min(Y)) + 1;
    fprintf('Adjusted Y range: [%.4f, %.4f]\n', min(Y), max(Y));
end

% 对Y进行对数变换
log_Y = log(Y);
fprintf('Log-transformed Y range: [%.4f, %.4f]\n', min(log_Y), max(log_Y));

% 获取自变量
x_vars = setdiff(var_names, {y_var});

% 移除缺失值过多的变量
valid_x_vars = {};
for i = 1:length(x_vars)
    var_data = numeric_data.(x_vars{i});
    missing_ratio = sum(isnan(var_data)) / length(var_data);
    if missing_ratio < 0.5
        valid_x_vars{end+1} = x_vars{i};
    end
end

fprintf('Selected %d independent variables\n', length(valid_x_vars));

% 构建自变量矩阵
X = [];
for i = 1:length(valid_x_vars)
    var_data = numeric_data.(valid_x_vars{i});
    % 用均值填充缺失值
    var_data(isnan(var_data)) = nanmean(var_data);
    X = [X, var_data];
end

% 添加常数项
X = [ones(size(X, 1), 1), X];
var_names_full = ['Intercept', valid_x_vars];

fprintf('Final data shape: X(%dx%d), Y(%dx1)\n', size(X), length(log_Y));

%% 3. 多元线性回归分析
fprintf('\nPerforming multiple linear regression...\n');

% 计算回归系数
beta = (X' * X) \ (X' * log_Y);

% 预测值
log_Y_pred = X * beta;

% 计算评估指标
SS_res = sum((log_Y - log_Y_pred).^2);
SS_tot = sum((log_Y - mean(log_Y)).^2);
R_squared = 1 - SS_res / SS_tot;
MSE = SS_res / (length(log_Y) - length(beta));
RMSE = sqrt(MSE);

% 显示结果
fprintf('\n============================================================\n');
fprintf('Log-Transform Multiple Linear Regression Results\n');
fprintf('============================================================\n');

% 构建回归方程
fprintf('\nRegression Equation:\n');
equation_str = sprintf('ln(Y) = %.4f', beta(1));
for i = 2:length(beta)
    if beta(i) >= 0
        equation_str = [equation_str, sprintf(' + %.4f*%s', beta(i), var_names_full{i})];
    else
        equation_str = [equation_str, sprintf(' %.4f*%s', beta(i), var_names_full{i})];
    end
end
fprintf('%s\n', equation_str);

fprintf('\nModel Evaluation Metrics:\n');
fprintf('R² (R-squared): %.4f\n', R_squared);
fprintf('MSE (Mean Squared Error): %.4f\n', MSE);
fprintf('RMSE (Root Mean Squared Error): %.4f\n', RMSE);

fprintf('\nRegression Coefficients:\n');
for i = 1:length(beta)
    fprintf('%s: %.4f\n', var_names_full{i}, beta(i));
end

%% 4. 可视化
fprintf('\nGenerating visualizations...\n');

figure('Position', [100, 100, 1200, 800]);

% 1. 实际值vs预测值
subplot(2, 3, 1);
scatter(log_Y, log_Y_pred, 50, 'b', 'filled', 'Alpha', 0.6);
hold on;
plot([min(log_Y), max(log_Y)], [min(log_Y), max(log_Y)], 'r--', 'LineWidth', 2);
xlabel('Actual ln(Y)');
ylabel('Predicted ln(Y)');
title('Actual vs Predicted Values');
grid on;
text(0.05, 0.95, sprintf('R² = %.4f', R_squared), 'Units', 'normalized', ...
     'BackgroundColor', 'white', 'EdgeColor', 'black');

% 2. 残差图
subplot(2, 3, 2);
residuals = log_Y - log_Y_pred;
scatter(log_Y_pred, residuals, 50, 'g', 'filled', 'Alpha', 0.6);
hold on;
yline(0, 'r--', 'LineWidth', 2);
xlabel('Predicted ln(Y)');
ylabel('Residuals');
title('Residual Plot');
grid on;

% 3. 残差直方图
subplot(2, 3, 3);
histogram(residuals, 20, 'FaceColor', [1, 0.5, 0], 'EdgeColor', 'black');
xlabel('Residuals');
ylabel('Frequency');
title('Residual Distribution');
grid on;

% 4. Q-Q图
subplot(2, 3, 4);
qqplot(residuals);
title('Q-Q Plot (Normality Test)');
grid on;

% 5. 回归系数图
subplot(2, 3, 5);
coeffs = beta(2:end);
var_names_plot = var_names_full(2:end);

colors = zeros(length(coeffs), 3);
for i = 1:length(coeffs)
    if coeffs(i) >= 0
        colors(i, :) = [0, 0, 1];
    else
        colors(i, :) = [1, 0, 0];
    end
end

bar_handle = bar(1:length(coeffs), coeffs, 'FaceColor', 'flat');
bar_handle.CData = colors;

xlabel('Variables');
ylabel('Coefficients');
title('Regression Coefficients');
set(gca, 'XTick', 1:length(var_names_plot), 'XTickLabel', var_names_plot, 'XTickLabelRotation', 45);
grid on;

% 6. 相关性矩阵
subplot(2, 3, 6);
corr_data = [X(:, 2:end), log_Y];
corr_matrix = corrcoef(corr_data);
imagesc(corr_matrix);
colorbar;
colormap('coolwarm');
caxis([-1, 1]);
title('Correlation Matrix');

sgtitle('Y Concentration Log-Transform Multiple Linear Regression Analysis');

% 保存图片
saveas(gcf, 'matlab_simple_analysis.png');
fprintf('Figure saved as: matlab_simple_analysis.png\n');

fprintf('\n============================================================\n');
fprintf('Analysis completed!\n');
fprintf('============================================================\n');
