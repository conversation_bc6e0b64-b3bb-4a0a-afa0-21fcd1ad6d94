%% Y Concentration Log-Transform Multiple Linear Regression Analysis
% Y浓度对数变换多元线性回归分析
% Author: Data Analysis Team
% Date: January 2025

clear; clc; close all;

% Configure Chinese font support - 配置中文字体支持
try
    if ispc
        % Windows system - Windows系统
        set(0,'DefaultAxesFontName','Microsoft YaHei');
        set(0,'DefaultTextFontName','Microsoft YaHei');
        set(0,'DefaultUicontrolFontName','Microsoft YaHei');
    elseif ismac
        % macOS system - macOS系统
        set(0,'DefaultAxesFontName','PingFang SC');
        set(0,'DefaultTextFontName','PingFang SC');
        set(0,'DefaultUicontrolFontName','PingFang SC');
    else
        % Linux system - Linux系统
        set(0,'DefaultAxesFontName','DejaVu Sans');
        set(0,'DefaultTextFontName','DejaVu Sans');
        set(0,'DefaultUicontrolFontName','DejaVu Sans');
    end
    set(0,'DefaultAxesFontSize',12);
    set(0,'DefaultTextFontSize',12);
    fprintf('Font configuration completed successfully.\n');
    fprintf('字体配置完成。\n');
catch
    fprintf('Warning: Font configuration failed, Chinese characters may not display correctly.\n');
    fprintf('警告：字体配置失败，中文可能显示异常。\n');
end

fprintf('============================================================\n');
fprintf('Y Concentration Log-Transform Multiple Linear Regression\n');
fprintf('Y浓度对数变换多元线性回归分析\n');
fprintf('============================================================\n');

%% 1. Data Loading - 数据加载
try
    % Read Excel file - 读取Excel文件
    data = readtable('999.xlsx');
    fprintf('Data loaded successfully! 数据加载成功！\n');
    fprintf('Data dimensions: %d rows x %d columns\n', height(data), width(data));
    fprintf('数据维度：%d行 x %d列\n', height(data), width(data));
    
    % Display variable names - 显示变量名
    fprintf('\nVariable names (变量名):\n');
    var_names = data.Properties.VariableNames;
    for i = 1:min(10, length(var_names))  % Show first 10 variables
        fprintf('%d. %s\n', i, var_names{i});
    end
    if length(var_names) > 10
        fprintf('... and %d more variables\n', length(var_names) - 10);
    end
    
catch ME
    fprintf('Failed to read Excel file: %s\n', ME.message);
    fprintf('Excel文件读取失败：%s\n', ME.message);
    fprintf('Creating sample data for demonstration...\n');
    fprintf('创建示例数据进行演示...\n');
    
    % Create sample data - 创建示例数据
    rng(42);
    n = 100;
    
    age = normrnd(29, 3.6, n, 1);
    height = normrnd(161, 5.1, n, 1);
    weight = normrnd(83, 9.2, n, 1);
    bmi = weight ./ (height/100).^2;
    gc_content = normrnd(0.42, 0.02, n, 1);
    y_concentration = exp(-2.5 + 0.01*age + 0.005*height - 0.003*weight + ...
                         2*gc_content + normrnd(0, 0.1, n, 1));
    
    data = table(age, height, weight, bmi, gc_content, y_concentration, ...
                'VariableNames', {'Age', 'Height', 'Weight', 'BMI', 'GC_Content', 'Y_Concentration'});
    
    fprintf('Sample data created successfully! 示例数据创建成功！\n');
    fprintf('Data dimensions: %d rows x %d columns\n', height(data), width(data));
end

%% 2. Data Preprocessing - 数据预处理
fprintf('\nData preprocessing... 数据预处理中...\n');

% Get numeric variables - 获取数值型变量
numeric_vars = varfun(@isnumeric, data, 'output', 'uniform');
numeric_data = data(:, numeric_vars);
all_var_names = numeric_data.Properties.VariableNames;

% Find Y variable - 寻找Y变量
y_var = '';
y_patterns = {'Y_Concentration', 'Y____', 'y_concentration', 'Y浓度', 'Y染色体浓度'};

for pattern = y_patterns
    for i = 1:length(all_var_names)
        if contains(all_var_names{i}, pattern{1})
            y_var = all_var_names{i};
            break;
        end
    end
    if ~isempty(y_var), break; end
end

% If not found, look for variables containing 'Y'
if isempty(y_var)
    for i = 1:length(all_var_names)
        if contains(upper(all_var_names{i}), 'Y')
            y_var = all_var_names{i};
            break;
        end
    end
end

% If still not found, use the last variable
if isempty(y_var)
    y_var = all_var_names{end};
end

fprintf('Selected Y variable: %s\n', y_var);
fprintf('选择的Y变量：%s\n', y_var);

% Extract Y data - 提取Y数据
Y = numeric_data.(y_var);

% Handle non-positive values - 处理非正值
if any(Y <= 0)
    fprintf('Warning: Non-positive values detected in Y, adjusting...\n');
    fprintf('警告：检测到Y中有非正值，正在调整...\n');
    Y = Y + abs(min(Y)) + 1;
    fprintf('Y range after adjustment: [%.4f, %.4f]\n', min(Y), max(Y));
    fprintf('调整后Y的范围：[%.4f, %.4f]\n', min(Y), max(Y));
end

% Log transformation - 对数变换
log_Y = log(Y);
fprintf('Log-transformed Y range: [%.4f, %.4f]\n', min(log_Y), max(log_Y));
fprintf('对数变换后Y的范围：[%.4f, %.4f]\n', min(log_Y), max(log_Y));

% Select independent variables - 选择自变量
x_vars = setdiff(all_var_names, {y_var});
valid_x_vars = {};

fprintf('Selecting variables with <30%% missing values...\n');
fprintf('选择缺失值<30%%的变量...\n');

for i = 1:length(x_vars)
    var_data = numeric_data.(x_vars{i});
    missing_ratio = sum(isnan(var_data)) / length(var_data);
    if missing_ratio < 0.3
        valid_x_vars{end+1} = x_vars{i};
    end
end

% Limit number of variables to avoid overfitting - 限制变量数量避免过拟合
max_vars = min(8, floor(length(log_Y)/15));
if length(valid_x_vars) > max_vars
    fprintf('Too many variables (%d), selecting top %d based on correlation...\n', ...
            length(valid_x_vars), max_vars);
    fprintf('变量过多（%d个），基于相关性选择前%d个...\n', length(valid_x_vars), max_vars);
    
    % Calculate correlations with Y - 计算与Y的相关性
    correlations = zeros(length(valid_x_vars), 1);
    for i = 1:length(valid_x_vars)
        var_data = numeric_data.(valid_x_vars{i});
        var_data(isnan(var_data)) = nanmean(var_data);
        correlations(i) = abs(corr(var_data, log_Y, 'rows', 'complete'));
    end
    
    [~, sorted_idx] = sort(correlations, 'descend');
    valid_x_vars = valid_x_vars(sorted_idx(1:max_vars));
end

fprintf('Selected %d independent variables\n', length(valid_x_vars));
fprintf('选择了%d个自变量\n', length(valid_x_vars));

% Build design matrix - 构建设计矩阵
X = ones(length(log_Y), 1);  % Intercept term - 截距项
var_names_full = {'Intercept'};

for i = 1:length(valid_x_vars)
    var_data = numeric_data.(valid_x_vars{i});
    % Fill missing values with mean - 用均值填充缺失值
    var_data(isnan(var_data)) = nanmean(var_data);
    % Standardize to reduce numerical issues - 标准化以减少数值问题
    var_data = (var_data - mean(var_data)) / std(var_data);
    X = [X, var_data];
    var_names_full{end+1} = valid_x_vars{i};
end

fprintf('Design matrix dimensions: %dx%d\n', size(X));
fprintf('设计矩阵维度：%dx%d\n', size(X));

%% 3. Regression Analysis - 回归分析
fprintf('\nPerforming regression analysis... 进行回归分析...\n');

% Check condition number - 检查条件数
cond_num = cond(X'*X);
fprintf('Matrix condition number: %.2e\n', cond_num);
fprintf('矩阵条件数：%.2e\n', cond_num);

if cond_num > 1e12
    fprintf('Warning: Matrix is ill-conditioned, using ridge regression...\n');
    fprintf('警告：矩阵病态，使用岭回归...\n');
    lambda = 0.01;
    beta = (X'*X + lambda*eye(size(X,2))) \ (X'*log_Y);
else
    % Ordinary least squares - 普通最小二乘
    beta = (X'*X) \ (X'*log_Y);
end

% Predictions and evaluation - 预测和评估
log_Y_pred = X * beta;
residuals = log_Y - log_Y_pred;

SS_res = sum(residuals.^2);
SS_tot = sum((log_Y - mean(log_Y)).^2);
R_squared = 1 - SS_res / SS_tot;
MSE = SS_res / (length(log_Y) - length(beta));
RMSE = sqrt(MSE);

%% 4. Results Display - 结果显示
fprintf('\n============================================================\n');
fprintf('REGRESSION RESULTS 回归结果\n');
fprintf('============================================================\n');

fprintf('\nRegression Equation 回归方程:\n');
eq_str = sprintf('ln(Y) = %.4f', beta(1));
for i = 2:length(beta)
    if beta(i) >= 0
        eq_str = [eq_str, sprintf(' + %.4f*%s', beta(i), var_names_full{i})];
    else
        eq_str = [eq_str, sprintf(' %.4f*%s', beta(i), var_names_full{i})];
    end
end
fprintf('%s\n', eq_str);

fprintf('\nModel Performance 模型性能:\n');
fprintf('R-squared (决定系数): %.4f\n', R_squared);
fprintf('RMSE (均方根误差): %.4f\n', RMSE);
fprintf('Sample size (样本数): %d\n', length(log_Y));

fprintf('\nCoefficients 回归系数:\n');
for i = 1:length(beta)
    fprintf('%-20s: %8.4f\n', var_names_full{i}, beta(i));
end

%% 5. Visualization - 可视化
fprintf('\nGenerating visualizations... 生成可视化图表...\n');

% Create figure with English labels to avoid encoding issues
% 使用英文标签创建图形以避免编码问题
figure('Position', [100, 100, 1200, 800], 'Name', 'Y Concentration Regression Analysis');

% 1. Actual vs Predicted - 实际值vs预测值
subplot(2, 3, 1);
scatter(log_Y, log_Y_pred, 30, 'b', 'filled');
hold on;
plot([min(log_Y), max(log_Y)], [min(log_Y), max(log_Y)], 'r--', 'LineWidth', 2);
xlabel('Actual ln(Y)');
ylabel('Predicted ln(Y)');
title('Actual vs Predicted Values');
grid on;
text(0.05, 0.95, sprintf('R^2 = %.3f', R_squared), 'Units', 'normalized', ...
     'BackgroundColor', 'white', 'FontSize', 10);

% 2. Residual plot - 残差图
subplot(2, 3, 2);
scatter(log_Y_pred, residuals, 30, 'g', 'filled');
hold on;
plot([min(log_Y_pred), max(log_Y_pred)], [0, 0], 'r--', 'LineWidth', 2);
xlabel('Predicted ln(Y)');
ylabel('Residuals');
title('Residual Plot');
grid on;

% 3. Residual histogram - 残差直方图
subplot(2, 3, 3);
hist(residuals, 15);
xlabel('Residuals');
ylabel('Frequency');
title('Residual Distribution');
grid on;

% 4. Q-Q plot - Q-Q图
subplot(2, 3, 4);
try
    qqplot(residuals);
    title('Q-Q Plot (Normality Test)');
catch
    % Fallback if qqplot is not available
    plot(sort(residuals), 'o-');
    xlabel('Sample Index');
    ylabel('Sorted Residuals');
    title('Sorted Residuals Plot');
end
grid on;

% 5. Coefficient plot - 系数图
subplot(2, 3, 5);
coeffs = beta(2:end);  % Exclude intercept
bar(coeffs);
xlabel('Variable Index');
ylabel('Coefficient Value');
title('Regression Coefficients');
grid on;

% Add coefficient values on bars
for i = 1:length(coeffs)
    if coeffs(i) >= 0
        text(i, coeffs(i) + 0.01*max(abs(coeffs)), sprintf('%.3f', coeffs(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', 'FontSize', 8);
    else
        text(i, coeffs(i) - 0.01*max(abs(coeffs)), sprintf('%.3f', coeffs(i)), ...
             'HorizontalAlignment', 'center', 'VerticalAlignment', 'top', 'FontSize', 8);
    end
end

% 6. Fitted values plot - 拟合值图
subplot(2, 3, 6);
plot(1:length(log_Y), log_Y, 'bo-', 'MarkerSize', 3, 'LineWidth', 1);
hold on;
plot(1:length(log_Y), log_Y_pred, 'r.-', 'MarkerSize', 3, 'LineWidth', 1);
xlabel('Sample Index');
ylabel('ln(Y) Value');
title('Actual vs Fitted Values');
legend('Actual', 'Fitted', 'Location', 'best');
grid on;

% Add main title
sgtitle('Y Concentration Log-Transform Multiple Linear Regression Analysis');

% Save figure - 保存图形
try
    saveas(gcf, 'matlab_final_analysis.png');
    fprintf('Figure saved as: matlab_final_analysis.png\n');
    fprintf('图形已保存为：matlab_final_analysis.png\n');
catch
    fprintf('Warning: Could not save figure\n');
    fprintf('警告：无法保存图形\n');
end

%% 6. Save Results - 保存结果
fprintf('\nSaving results... 保存结果...\n');

% Create results table - 创建结果表
try
    results_table = table(var_names_full', beta, 'VariableNames', {'Variable', 'Coefficient'});
    writetable(results_table, 'matlab_regression_results.csv');
    fprintf('Results saved to: matlab_regression_results.csv\n');
    fprintf('结果已保存到：matlab_regression_results.csv\n');
catch
    fprintf('Warning: Could not save CSV file\n');
    fprintf('警告：无法保存CSV文件\n');
end

% Display summary in English to avoid encoding issues
% 用英文显示摘要以避免编码问题
fprintf('\n============================================================\n');
fprintf('ANALYSIS SUMMARY\n');
fprintf('============================================================\n');
fprintf('Model Performance:\n');
fprintf('- R-squared: %.4f (explains %.1f%% of variance)\n', R_squared, R_squared*100);
fprintf('- RMSE: %.4f\n', RMSE);
fprintf('- Sample size: %d\n', length(log_Y));
fprintf('- Number of predictors: %d\n', length(valid_x_vars));

fprintf('\nTop 3 Most Important Variables:\n');
[~, sorted_idx] = sort(abs(beta(2:end)), 'descend');
for i = 1:min(3, length(sorted_idx))
    idx = sorted_idx(i) + 1;  % +1 because we excluded intercept
    fprintf('%d. %s: %.4f\n', i, var_names_full{idx}, beta(idx));
end

fprintf('\nFiles Generated:\n');
fprintf('- matlab_final_analysis.png: Comprehensive analysis plots\n');
fprintf('- matlab_regression_results.csv: Regression coefficients table\n');

fprintf('\n============================================================\n');
fprintf('ANALYSIS COMPLETED SUCCESSFULLY!\n');
fprintf('分析成功完成！\n');
fprintf('============================================================\n');
